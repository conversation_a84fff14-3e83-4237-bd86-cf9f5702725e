export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { captureApiError } from '@/lib/rollbar';

interface AccountData {
  user: {
    id: string;
    email: string;
    full_name?: string;
    created_at: string;
    last_sign_in_at?: string;
  };
  profile: {
    id: string;
    created_at: string;
    tokens: number;
    resume_file_name?: string;
    resume_url?: string;
    resume_uploaded_at?: string;
    real_file_name?: string;
  } | null;
  purchases: Array<{
    id: string;
    status: string;
    created_at: string;
    invoice_id: string;
    invoice_url?: string;
    amount: number;
    currency: string;
    token_amount: number;
    token_package: string;
    payment_method: string;
    payment_completed_at?: string;
  }>;
  letters: Array<{
    id: string;
    template_id: string;
    created_at: string;
    updated_at: string;
  }>;
  resumes: Array<{
    id: string;
    template_id?: string;
    status: string;
    tokens_deducted: boolean;
    created_at: string;
    updated_at: string;
  }>;
  emails: Array<{
    id: string;
    subject: string;
    created_at: string;
    updated_at: string;
  }>;
  feedback: Array<{
    id: string;
    feedback_type: string;
    message: string;
    created_at: string;
  }>;
  export_metadata: {
    exported_at: string;
    export_version: string;
    total_records: {
      purchases: number;
      letters: number;
      resumes: number;
      emails: number;
      feedback: number;
    };
  };
}

export async function GET(request: NextRequest) {
  try {
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);
    
    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch user profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, created_at, tokens, resume_file_name, resume_url, resume_uploaded_at, real_file_name')
      .eq('id', user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error fetching profile:', profileError);
    }

    // Fetch user purchases
    const { data: purchases, error: purchasesError } = await supabase
      .from('purchases')
      .select(`
        id,
        status,
        created_at,
        invoice_id,
        invoice_url,
        amount,
        currency,
        token_amount,
        token_package,
        payment_method,
        payment_completed_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (purchasesError) {
      console.error('Error fetching purchases:', purchasesError);
    }

    // Fetch user letters (without sensitive content)
    const { data: letters, error: lettersError } = await supabase
      .from('letters')
      .select('id, template_id, created_at, updated_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (lettersError) {
      console.error('Error fetching letters:', lettersError);
    }

    // Fetch user resumes (without sensitive content)
    const { data: resumes, error: resumesError } = await supabase
      .from('resumes')
      .select('id, template_id, status, tokens_deducted, created_at, updated_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (resumesError) {
      console.error('Error fetching resumes:', resumesError);
    }

    // Fetch user emails (without sensitive content)
    const { data: emails, error: emailsError } = await supabase
      .from('emails')
      .select('id, subject, created_at, updated_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (emailsError) {
      console.error('Error fetching emails:', emailsError);
    }

    // Fetch user feedback (if any)
    const { data: feedback, error: feedbackError } = await supabase
      .from('user_feedback')
      .select('id, feedback_type, message, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (feedbackError) {
      console.error('Error fetching feedback:', feedbackError);
    }

    // Compile account data
    const accountData: AccountData = {
      user: {
        id: user.id,
        email: user.email || '',
        full_name: user.user_metadata?.full_name,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
      },
      profile: profile,
      purchases: purchases || [],
      letters: letters || [],
      resumes: resumes || [],
      emails: emails || [],
      feedback: feedback || [],
      export_metadata: {
        exported_at: new Date().toISOString(),
        export_version: '1.0',
        total_records: {
          purchases: (purchases || []).length,
          letters: (letters || []).length,
          resumes: (resumes || []).length,
          emails: (emails || []).length,
          feedback: (feedback || []).length,
        },
      },
    };

    // Return JSON data as downloadable file
    return new NextResponse(JSON.stringify(accountData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="Gigsta_Account_Data_${user.id}_${new Date().toISOString().split('T')[0]}.json"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
    
  } catch (error) {
    console.error('Error downloading account data:', error);
    
    // Capture error details with Rollbar
    captureApiError('download-account-data', error, {
      requestUrl: request.url,
    });
    
    return NextResponse.json({
      success: false,
      error: 'Failed to download account data. Please try again.'
    }, { status: 500 });
  }
}
